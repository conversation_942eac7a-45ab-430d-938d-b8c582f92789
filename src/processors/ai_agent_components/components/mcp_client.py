"""
MCP (Model Context Protocol) 客户端
负责与 zhipu-web-search-sse MCP 服务器通信
"""

import json
import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from loguru import logger

try:
    import httpx
    import websockets
    ASYNC_LIBS_AVAILABLE = True
except ImportError:
    ASYNC_LIBS_AVAILABLE = False
    logger.warning("异步库未安装，MCP客户端功能受限")


class MCPClient:
    """MCP 客户端"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化 MCP 客户端
        
        Args:
            config: MCP 配置
        """
        self.config = config or {}
        self.server_url = self.config.get('server_url', 'ws://localhost:8080')
        self.api_key = self.config.get('api_key', '')
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        
        # 连接状态
        self.websocket = None
        self.is_connected = False
        
        # 请求ID计数器
        self.request_id = 0
    
    async def connect(self) -> bool:
        """
        连接到 MCP 服务器
        
        Returns:
            bool: 连接是否成功
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法连接MCP服务器")
            return False
        
        try:
            logger.info(f"尝试连接到MCP服务器: {self.server_url}")
            
            # 构建连接头
            headers = {}
            if self.api_key:
                headers['Authorization'] = f'Bearer {self.api_key}'
            
            # 连接WebSocket
            self.websocket = await websockets.connect(
                self.server_url,
                extra_headers=headers,
                timeout=self.timeout
            )
            
            self.is_connected = True
            logger.info("MCP服务器连接成功")
            return True
            
        except Exception as e:
            logger.error(f"连接MCP服务器失败: {e}")
            self.is_connected = False
            return False
    
    async def disconnect(self):
        """断开与 MCP 服务器的连接"""
        if self.websocket and self.is_connected:
            try:
                await self.websocket.close()
                logger.info("MCP服务器连接已断开")
            except Exception as e:
                logger.error(f"断开MCP服务器连接时出错: {e}")
            finally:
                self.is_connected = False
                self.websocket = None
    
    async def send_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送请求到 MCP 服务器
        
        Args:
            method: 方法名
            params: 参数
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        if not self.is_connected:
            if not await self.connect():
                raise Exception("无法连接到MCP服务器")
        
        # 构建请求
        self.request_id += 1
        request = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params
        }
        
        try:
            # 发送请求
            await self.websocket.send(json.dumps(request))
            logger.debug(f"发送MCP请求: {method}")
            
            # 等待响应
            response_text = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=self.timeout
            )
            
            response = json.loads(response_text)
            logger.debug(f"收到MCP响应: {response.get('id')}")
            
            # 检查错误
            if 'error' in response:
                error = response['error']
                raise Exception(f"MCP错误: {error.get('message', '未知错误')}")
            
            return response.get('result', {})
            
        except asyncio.TimeoutError:
            logger.error(f"MCP请求超时: {method}")
            raise Exception("MCP请求超时")
        except Exception as e:
            logger.error(f"MCP请求失败: {e}")
            raise
    
    async def search_web(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        执行网络搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        params = {
            "query": query,
            "max_results": kwargs.get('max_results', 5),
            "language": kwargs.get('language', 'zh'),
            "region": kwargs.get('region', 'CN'),
            "search_type": kwargs.get('search_type', 'general')
        }
        
        try:
            result = await self.send_request("web_search", params)
            return result.get('results', [])
        except Exception as e:
            logger.error(f"网络搜索失败: {e}")
            return []
    
    def search_web_sync(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        同步版本的网络搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        if not ASYNC_LIBS_AVAILABLE:
            logger.error("异步库未安装，无法执行MCP搜索")
            return []
        
        try:
            # 创建新的事件循环或使用现有的
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(
                            lambda: asyncio.run(self.search_web(query, **kwargs))
                        )
                        return future.result(timeout=self.timeout)
                else:
                    return loop.run_until_complete(self.search_web(query, **kwargs))
            except RuntimeError:
                # 没有事件循环，创建新的
                return asyncio.run(self.search_web(query, **kwargs))
                
        except Exception as e:
            logger.error(f"同步网络搜索失败: {e}")
            return []
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()


class FallbackWebSearcher:
    """备用网络搜索器（当MCP不可用时使用）"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化备用搜索器
        
        Args:
            config: 搜索配置
        """
        self.config = config or {}
        self.timeout = self.config.get('timeout', 30)
        self.user_agent = self.config.get(
            'user_agent', 
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
    
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        执行备用搜索
        
        Args:
            query: 搜索查询
            **kwargs: 其他搜索参数
            
        Returns:
            List[Dict[str, Any]]: 搜索结果
        """
        try:
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse
            
            # 构建搜索URL（使用DuckDuckGo作为备用）
            encoded_query = urllib.parse.quote_plus(query)
            search_url = f"https://duckduckgo.com/html/?q={encoded_query}"
            
            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }
            
            # 发送请求
            response = requests.get(search_url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 解析结果
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # 查找搜索结果
            result_elements = soup.find_all('div', class_='result')
            max_results = kwargs.get('max_results', 5)
            
            for element in result_elements[:max_results]:
                try:
                    # 提取标题
                    title_elem = element.find('a', class_='result__a')
                    title = title_elem.get_text(strip=True) if title_elem else ''
                    
                    # 提取URL
                    url = title_elem.get('href', '') if title_elem else ''
                    
                    # 提取摘要
                    snippet_elem = element.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                    
                    if title and url:
                        results.append({
                            'title': title,
                            'url': url,
                            'snippet': snippet,
                            'source': 'DuckDuckGo',
                            'publish_date': None
                        })
                        
                except Exception as e:
                    logger.debug(f"解析搜索结果项失败: {e}")
                    continue
            
            logger.info(f"备用搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"备用搜索失败: {e}")
            return []
