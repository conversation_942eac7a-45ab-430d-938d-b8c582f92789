"""
搜索处理器组件
负责执行网络搜索，集成zhipu-web-search-sse MCP
"""

import time
import json
from typing import Dict, Any, List, Optional
from loguru import logger

from ..models.search_models import SearchRequest, SearchResult, SearchContext, SearchType

# 导入MCP客户端和备用搜索器
try:
    from .mcp_client import MCPClient, FallbackWebSearcher
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logger.warning("MCP客户端不可用，将使用备用搜索方案")


class SearchHandler:
    """网络搜索处理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化搜索处理器

        Args:
            config: 搜索配置
        """
        self.config = config or {}
        self.max_results = self.config.get('max_results', 5)
        self.timeout = self.config.get('timeout', 30)
        self.default_language = self.config.get('language', 'zh')
        self.default_region = self.config.get('region', 'CN')

        # 搜索质量评估权重
        self.quality_weights = {
            'title_relevance': 0.3,
            'snippet_relevance': 0.4,
            'source_authority': 0.2,
            'freshness': 0.1
        }

        # 初始化搜索客户端
        self.mcp_client = None
        self.fallback_searcher = None
        self.search_mode = "mock"  # mock, mcp, fallback

        self._initialize_search_clients()

    def _initialize_search_clients(self):
        """初始化搜索客户端"""
        try:
            if MCP_AVAILABLE:
                # 尝试初始化MCP客户端
                mcp_config = self.config.get('mcp', {})
                if mcp_config.get('enabled', True):
                    self.mcp_client = MCPClient(mcp_config)
                    self.search_mode = "mcp"
                    logger.info("MCP搜索客户端初始化成功")
                else:
                    logger.info("MCP搜索已禁用")

            # 初始化备用搜索器
            if MCP_AVAILABLE:
                fallback_config = self.config.get('fallback', {})
                self.fallback_searcher = FallbackWebSearcher(fallback_config)
                if self.search_mode == "mock":
                    self.search_mode = "fallback"
                logger.info("备用搜索器初始化成功")

            logger.info(f"搜索模式: {self.search_mode}")

        except Exception as e:
            logger.error(f"搜索客户端初始化失败: {e}")
            self.search_mode = "mock"

    def search(self, query: str, search_type: SearchType = SearchType.GENERAL,
               max_results: Optional[int] = None) -> SearchContext:
        """
        执行网络搜索
        
        Args:
            query: 搜索查询
            search_type: 搜索类型
            max_results: 最大结果数
            
        Returns:
            SearchContext: 搜索上下文结果
        """
        start_time = time.time()
        
        # 创建搜索请求
        request = SearchRequest(
            query=query,
            search_type=search_type,
            max_results=max_results or self.max_results,
            language=self.default_language,
            region=self.default_region
        )
        
        logger.info(f"开始搜索: {query} (类型: {search_type.value})")
        
        try:
            # 执行搜索
            results = self._perform_search(request)
            
            # 计算搜索时间
            search_time = time.time() - start_time
            
            # 创建搜索上下文
            context = SearchContext(
                request=request,
                results=results,
                total_results=len(results),
                search_time=search_time
            )
            
            logger.info(f"搜索完成: 找到 {len(results)} 个结果，耗时 {search_time:.2f}秒")
            return context
            
        except Exception as e:
            error_msg = f"搜索失败: {str(e)}"
            logger.error(error_msg)
            
            return SearchContext(
                request=request,
                results=[],
                total_results=0,
                search_time=time.time() - start_time,
                error_message=error_msg
            )
    
    def _perform_search(self, request: SearchRequest) -> List[SearchResult]:
        """
        执行实际的搜索操作

        Args:
            request: 搜索请求

        Returns:
            List[SearchResult]: 搜索结果列表
        """
        try:
            # 根据搜索模式执行不同的搜索策略
            raw_results = []

            if self.search_mode == "mcp" and self.mcp_client:
                # 使用MCP客户端搜索
                raw_results = self._search_with_mcp(request)
            elif self.search_mode == "fallback" and self.fallback_searcher:
                # 使用备用搜索器
                raw_results = self._search_with_fallback(request)
            else:
                # 使用模拟搜索
                raw_results = self._mock_search_results(request)

            # 处理和评估搜索结果
            processed_results = []
            for result_data in raw_results:
                result = self._create_search_result(result_data, request.query)
                if result:
                    processed_results.append(result)

            # 按相关性排序
            processed_results.sort(key=lambda x: x.relevance_score, reverse=True)

            return processed_results[:request.max_results]

        except Exception as e:
            logger.error(f"执行搜索时出错: {e}")
            # 如果主要搜索方法失败，尝试备用方法
            if self.search_mode != "mock":
                logger.info("尝试使用模拟搜索作为备用")
                try:
                    mock_results = self._mock_search_results(request)
                    processed_results = []
                    for result_data in mock_results:
                        result = self._create_search_result(result_data, request.query)
                        if result:
                            processed_results.append(result)
                    return processed_results[:request.max_results]
                except Exception as fallback_error:
                    logger.error(f"备用搜索也失败: {fallback_error}")
            raise

    def _search_with_mcp(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """
        使用MCP客户端执行搜索

        Args:
            request: 搜索请求

        Returns:
            List[Dict[str, Any]]: 搜索结果数据
        """
        try:
            if not self.mcp_client:
                raise Exception("MCP客户端未初始化")

            # 调用MCP客户端搜索
            results = self.mcp_client.search_web_sync(
                query=request.query,
                max_results=request.max_results,
                language=request.language,
                region=request.region,
                search_type=request.search_type.value
            )

            logger.info(f"MCP搜索完成，找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"MCP搜索失败: {e}")
            # 如果MCP搜索失败，尝试备用搜索
            if self.fallback_searcher:
                logger.info("MCP搜索失败，尝试备用搜索")
                return self._search_with_fallback(request)
            else:
                raise

    def _search_with_fallback(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """
        使用备用搜索器执行搜索

        Args:
            request: 搜索请求

        Returns:
            List[Dict[str, Any]]: 搜索结果数据
        """
        try:
            if not self.fallback_searcher:
                raise Exception("备用搜索器未初始化")

            # 调用备用搜索器
            results = self.fallback_searcher.search(
                query=request.query,
                max_results=request.max_results,
                language=request.language,
                region=request.region
            )

            logger.info(f"备用搜索完成，找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"备用搜索失败: {e}")
            raise

    def _mock_search_results(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """
        模拟搜索结果（实际实现时应替换为真实的MCP调用）
        
        Args:
            request: 搜索请求
            
        Returns:
            List[Dict[str, Any]]: 模拟的搜索结果数据
        """
        # 这是一个模拟实现，实际使用时需要调用zhipu-web-search-sse MCP
        mock_data = [
            {
                "title": f"关于 {request.query} 的详细介绍",
                "url": "https://example.com/article1",
                "snippet": f"这是关于 {request.query} 的详细说明和分析...",
                "source": "权威网站",
                "publish_date": "2024-01-15"
            },
            {
                "title": f"{request.query} 最新发展趋势",
                "url": "https://example.com/article2", 
                "snippet": f"最新的 {request.query} 发展趋势和市场分析...",
                "source": "行业报告",
                "publish_date": "2024-01-10"
            },
            {
                "title": f"如何理解 {request.query}",
                "url": "https://example.com/article3",
                "snippet": f"深入解析 {request.query} 的核心概念和应用...",
                "source": "专业博客",
                "publish_date": "2024-01-05"
            }
        ]
        
        return mock_data
    
    def _create_search_result(self, result_data: Dict[str, Any], query: str) -> Optional[SearchResult]:
        """
        创建搜索结果对象
        
        Args:
            result_data: 原始搜索结果数据
            query: 搜索查询
            
        Returns:
            Optional[SearchResult]: 搜索结果对象
        """
        try:
            # 计算相关性评分
            relevance_score = self._calculate_relevance_score(result_data, query)
            
            return SearchResult(
                title=result_data.get('title', ''),
                url=result_data.get('url', ''),
                snippet=result_data.get('snippet', ''),
                source=result_data.get('source', ''),
                relevance_score=relevance_score,
                publish_date=result_data.get('publish_date'),
                content=result_data.get('content')
            )
            
        except Exception as e:
            logger.error(f"创建搜索结果对象失败: {e}")
            return None
    
    def _calculate_relevance_score(self, result_data: Dict[str, Any], query: str) -> float:
        """
        计算搜索结果的相关性评分
        
        Args:
            result_data: 搜索结果数据
            query: 搜索查询
            
        Returns:
            float: 相关性评分 (0-1)
        """
        try:
            score = 0.0
            query_lower = query.lower()
            
            # 标题相关性
            title = result_data.get('title', '').lower()
            title_score = self._text_similarity(title, query_lower)
            score += title_score * self.quality_weights['title_relevance']
            
            # 摘要相关性
            snippet = result_data.get('snippet', '').lower()
            snippet_score = self._text_similarity(snippet, query_lower)
            score += snippet_score * self.quality_weights['snippet_relevance']
            
            # 来源权威性（简单实现）
            source = result_data.get('source', '').lower()
            authority_score = self._calculate_source_authority(source)
            score += authority_score * self.quality_weights['source_authority']
            
            # 时效性
            freshness_score = self._calculate_freshness(result_data.get('publish_date'))
            score += freshness_score * self.quality_weights['freshness']
            
            return min(score, 1.0)  # 确保不超过1.0
            
        except Exception as e:
            logger.error(f"计算相关性评分失败: {e}")
            return 0.5  # 返回中等评分
    
    def _text_similarity(self, text: str, query: str) -> float:
        """
        计算文本相似度（简单实现）
        
        Args:
            text: 文本内容
            query: 查询内容
            
        Returns:
            float: 相似度评分 (0-1)
        """
        if not text or not query:
            return 0.0
        
        # 简单的关键词匹配
        query_words = set(query.split())
        text_words = set(text.split())
        
        if not query_words:
            return 0.0
        
        # 计算交集比例
        intersection = query_words.intersection(text_words)
        return len(intersection) / len(query_words)
    
    def _calculate_source_authority(self, source: str) -> float:
        """
        计算来源权威性评分
        
        Args:
            source: 来源名称
            
        Returns:
            float: 权威性评分 (0-1)
        """
        # 简单的权威性评估
        authority_keywords = ['官方', '政府', '大学', '研究院', '权威', '专业']
        
        for keyword in authority_keywords:
            if keyword in source:
                return 0.8
        
        return 0.5  # 默认中等权威性
    
    def _calculate_freshness(self, publish_date: Optional[str]) -> float:
        """
        计算时效性评分
        
        Args:
            publish_date: 发布日期
            
        Returns:
            float: 时效性评分 (0-1)
        """
        if not publish_date:
            return 0.5  # 无日期信息，返回中等评分
        
        try:
            from datetime import datetime, timedelta
            
            # 解析日期（简单实现）
            pub_date = datetime.strptime(publish_date, '%Y-%m-%d')
            now = datetime.now()
            days_ago = (now - pub_date).days
            
            # 越新的内容评分越高
            if days_ago <= 7:
                return 1.0
            elif days_ago <= 30:
                return 0.8
            elif days_ago <= 90:
                return 0.6
            elif days_ago <= 365:
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5  # 解析失败，返回中等评分
    
    def search_multiple_queries(self, queries: List[str], 
                              search_type: SearchType = SearchType.GENERAL) -> List[SearchContext]:
        """
        执行多个搜索查询
        
        Args:
            queries: 搜索查询列表
            search_type: 搜索类型
            
        Returns:
            List[SearchContext]: 搜索结果列表
        """
        results = []
        
        for query in queries:
            context = self.search(query, search_type)
            results.append(context)
            
            # 添加延迟以避免请求过于频繁
            time.sleep(0.5)
        
        return results
